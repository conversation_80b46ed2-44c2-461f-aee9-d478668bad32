# This is a basic workflow to help you get started with Actions

name: Release


on:
  push:
    tags:
      - "v*.*.*"

# A workflow that zip the source code and upload it as a release asset.
jobs:
    build:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Zip source code
              run: zip -r resume-ng-zh.zip .

            - name: Release
              uses: softprops/action-gh-release@v2
              if: startsWith(github.ref, 'refs/tags/')
              with:
                files: resume-ng-zh.zip

            - name: Checkout
              uses: actions/checkout@v4
              with:
                ref: english

            - name: Zip source code
              run: zip -r resume-ng-en.zip .

            - name: Release
              uses: softprops/action-gh-release@v2
              if: startsWith(github.ref, 'refs/tags/')
              with:
                files: resume-ng-en.zip