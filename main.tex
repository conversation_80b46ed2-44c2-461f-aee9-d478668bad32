% !TeX TS-program = xelatex

\documentclass{resume}
\ResumeName{朱祉睿}

% 如果想插入照片，请使用以下两个库。
\usepackage{graphicx}
\usepackage{tikz}

\begin{document}

\ResumeContacts{
  (+86)182-2939-5676,%
  \ResumeUrl{mailto:<EMAIL>}{<EMAIL>},%
  算法实习生 26届硕士在读%
}

% 如果想插入照片，请取消此代码的注释。
% 但是默认不推荐插入照片，因为这不是简历的重点。
% 如果默认的照片插入格式不能满足你的需求，你可以尝试调整照片的大小，或者使用其他的插入照片的方法。
% 不然，也可以先渲染 PDF 简历，然后用其他工具在 PDF 上叠加照片。
\begin{tikzpicture}[remember picture, overlay]
  \node [anchor=north east, inner sep=1cm]  at (current page.north east) 
     {\includegraphics[width=2cm]{image.png}};
\end{tikzpicture}


\ResumeTitle

\vspace{1em}

\section{教育经历}
\ResumeItem
[中山大学|硕士研究生]
{中山大学}
[\textnormal{应用统计，数学学院|}  硕士（保研）]
[2024.09—2026.06]

\ResumeItem
[南方科技大学|本科生]
{南方科技大学}
[\textnormal{统计学，统计与数据科学学院|} 学士]
[2020.09—2024.06]

\vspace{1em}

\section{实习经历}

\ResumeItem{快手}
[商业化~广告算法实习]
[2025.05—2025.08]

\begin{itemize}
  \item \textbf{项目一：搜索广告表单停留时长建模}
  \begin{itemize}
    \item \textbf{背景:} 在搜索广告表单场景中，用户停留时长是其转化意向的关键指标，但基线模型仅浅度利用该信号。本项目旨在升级时长建模方式，为主任务提供更可靠的bias信息，提升转化预估准确性。
    \item \textbf{技术方案:}
    \begin{itemize}
      \item \textbf{数据工程与增强：} 基于搜索广告日志，构建了从用户进入到关闭表单页的完整行为序列，精确计算\textbf{Leave Time}，并关联用户点击、转化等多维度行为，为模型提供高質量训练数据。
      \item \textbf{模型训练与优化：} 1) 引入\textbf{保序回归}思想，将Leave Time连续值预测转化为一系列有序的二分类子任务，并设计了“前细后粗”的\textbf{非均匀分桶}策略，以适应样本量随时间变化的分布特性。 2) 借鉴\textbf{ESMM}结构，对分桶间的时序依赖关系进行建模，通过概率连乘方式优化传统保序回归，并引入\textbf{ZILN网络}解决大量零停留时长样本的问题，最终将Leave Time预测网络作为\textbf{辅助任务}与\textbf{CVR主任务}融合，提升整体预估准确性。
    \end{itemize}
    \item \textbf{项目成果:} 在搜索表单场景下，模型上线后（10\%真实流量），核心业务指标取得显著提升：\textbf{客户预期ROI花费提升4.082\%}，\textbf{表单转换数提升3.201\%}，证明了精细化时长建模对业务的有效增益。
  \end{itemize}
  \item \textbf{项目一：直播智能发券系统QCPX}
  \begin{itemize}
    \item \textbf{背景：} 为提升电商广告场景的投放效率和商业化收入，优化了直播场景智能优惠券系统。该系统通过向价格敏感用户精准发放商家资助的优惠券，旨在提升广告转化率和客户消耗。
    \item \textbf{核心职责与成果：}
    \begin{itemize}
        \item \textbf{设计并实现核心算法模型：}针对旧方案（树模型）时效性差、预估不准的问题，创新性地设计并实现了\textbf{实时多处置（Multi-treatment）CVR Uplift 模型}。该模型能够实时、弹性地预测不同面额优惠券对用户转化率的提升效果（HTE），显著增强了预估的精准度。
        \item \textbf{构建因果推断框架：}为保证模型评估的无偏性，搭建了一套基于\textbf{随机对照试验（RCT）}的严格\textbf{因果标签体系}，确保了在复杂发券场景下对增量转化的准确衡量与建模。
        \item \textbf{优化发券与计费策略：}开发了\textbf{多面额优惠券优选机制}和 \textbf{mROI Pacing 调控策略}。前者通过实时求解最优化问题（`argmax(eCPM)`) 来决定最佳券面额，后者则通过引入调控因子$\alpha$来动态平衡发券成本与广告收入，确保发券ROI的稳定可控。
        \item \textbf{取得显著业务收益：}项目上线后，在核心的内循环业务场景中，实现了\textbf{广告预期花费提升1.93\%}，\textbf{实际消耗提升0.73\%} 的显著效果，同时\textbf{发券边际投资回报率（mROI）高达2.14}，证明了策略的有效性和商业价值。
    \end{itemize}
  \end{itemize}
\end{itemize}

\vspace{0.5em}
\noindent\rule{\textwidth}{0.4pt}
\vspace{0.3em}

\ResumeItem{字节跳动}
[业务中台~大模型算法实习]
[2025.02—2025.05]

\begin{itemize}
  \item \textbf{项目一：基于 LLM 的 AI LQA功能效果调优}
  \begin{itemize}
    \item \textbf{背景:} 字节跳动 STARLING 平台的 AI LQA 功能旨在自动进行翻译质检，但因准确率低 (基线合格率<65\%) 且误报严重，阻碍了 TikTok、PDI 等多业务线的有效应用及自动化流程。
    \item \textbf{技术方案:}
    \begin{itemize}
      \item \textbf{数据工程与增强：} 1) 利用平台的LQA 数据，借助 Gemini-2.5-Pro 模型及人工反馈，构造\textbf{COT数据}，为训练样本生成精细化的错误分析论证过程。应用 \textbf{Pairwise 数据}构建策略，将同一原文的参考翻译与错误翻译配对输入，强化模型对正负样本的对比学习能力。 2) 针对复杂错误类型如词汇遗漏、词汇不当等与数据不平衡问题，采用\textbf{投票机制筛选高质量 COT 数据}，并显著扩增训练样本，有效攻克检测难点。
      \item \textbf{模型训练与优化：} 对 \textbf{Qwen2.5-7B-Instruct} 进行基于 \textbf{LoRA} 的 \textbf{SFT} ；优化模型输出包含判错结果及结构化推理过程的响应，提升结果可靠性。
    \end{itemize}
    \item \textbf{项目成果:} 核心指标突破： 成功将 AI LQA 模型的判错二分类\textbf{Recall与Precision均稳定提升至 90\% 以上}，远超基线水平 (<64\%)，达成项目核心优化目标。
  \end{itemize}
  \item \textbf{项目二：基于 LLM 的 AI翻译调优}
  \begin{itemize}
    \item \textbf{背景:} 旨在直接提升特定业务场景（如 TT 话题/文档、PDI 中英翻译）的机器翻译质量。
    \item \textbf{技术方案:}
    \begin{itemize}
      \item \textbf{RL算法实践与奖励函数设计：} 采用 \textbf{GRPO} 对 \textbf{Qwen2.5-7B-Instruct} 进行Post-Training，设计结合 BLEU、TER 等自动化指标的\textbf{句级 Reward 函数}，增强其对模型优化方向的引导能力。
      \item \textbf{Agent链路搭建：} 构建\textbf{LLM多阶段自主优化翻译流程}，引入术语表以及风格指南，融合多维规范的\textbf{Agent反思与迭代修正核心}，引导及规范化译文。
    \end{itemize}
    \item \textbf{项目成果:} 核心指标突破： 成功搭建 \textbf{RL 优化机器翻译}以及\textbf{Agent流程}，成功将\textbf{人工 LQA 分数稳定提升至 80\% 以上} ，超过基线水平 (<74\%)。
  \end{itemize}
\end{itemize}

\vspace{0.5em}
\noindent\rule{\textwidth}{0.4pt}
\vspace{0.3em}

\ResumeItem{三一集团}
[耕耘实验室 | 大模型算法实习]
[2024.09—2025.01]

\begin{itemize}
  \item \textbf{项目背景：}研发工业级\textbf{多模态智能体系统}，通过LLM实现复杂工业流程的智能决策与控制，解决传统方案人工依赖度高、规则泛化性差等痛点。
  \item \textbf{技术方案：}
  \begin{itemize}
    \item \textbf{数据集构建：} 针对含大量无语义标注数据、动作异常等结构性问题的原始数据集，进行有效筛选与过滤；通过对视觉信息随机剪裁等方法实施数据增强。构建\textbf{PromptBuilder}对话管理、系统提示集成和特殊标记处理。
    \item \textbf{模型选择与微调：} 构建了包含\textbf{SigLIP视觉编码器}、\textbf{Fused-GELU投影层}及\textbf{Qwen2大语言模型}的\textbf{多模态VLA框架}。构建\textbf{Action Tokenizer}，创新性采用\textbf{离散化映射}连续动作向量至语言模型词表中Special Token处。
    \item \textbf{模型训练与评估：} 进行\textbf{全参数端到端SFT}，监督模型学习预测动作向量。以L1 Loss，Action Accuracy等作为训练监控指标，最终在\textbf{Libero Benchmark}评估模型预测、执行动作能力。
  \end{itemize}
  \item \textbf{项目成果：}模型成功预测动作的\textbf{准确率达到86\%}，\textbf{内存占用降幅达67\%}，有效提升公司焊接工作效率和质量。
\end{itemize}

\vspace{1em}

\section{个人评价}
拥有扎实的深度学习算法基础和丰富的工业界实习经验，在广告算法优化、大模型微调与应用、多模态智能体开发等方向具有深度实践。熟悉端到端的机器学习项目流程，包括数据工程、模型设计、训练优化和效果评估。具备良好的工程实现能力和问题解决能力，能够将前沿算法技术与实际业务需求相结合，推动项目落地并取得显著成果。


\end{document}
